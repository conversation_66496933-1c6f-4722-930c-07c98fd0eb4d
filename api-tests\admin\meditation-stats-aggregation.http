### 冥想统计数据聚合接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

@baseUrl = http://localhost:3004
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU2NDQ2Mzc5LCJleHAiOjE3NTY1MzI3Nzl9.pLTL4DnK6BJEXCbDZpe7q9apiEcX9OuWxTlPiMR-Scw
@testUserId = 1
@testUserToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJvcGVuaWQiOiJvcGVuaWRfdGVzdF8xMjMiLCJpYXQiOjE3NTQ5MTUzMzIsImV4cCI6MTc1NzUwNzMzMn0.kkI_Fiw8KcrJETT8JTw5eDBLtUIj25EoAOeELtBwEj0

### 1. 生成所有类型的统计数据（所有用户）
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "all"
}

### 2. 生成周统计数据（所有用户）
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "week"
}

### 3. 生成月统计数据（所有用户）
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "month"
}

### 4. 生成年统计数据（所有用户）
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "year"
}

### 4.1. 生成历史周统计数据（所有用户，过去4周）
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "week_historical",
  "weeks_back": 4
}

### 4.2. 生成历史周统计数据（所有用户，过去8周）
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "week_historical",
  "weeks_back": 8
}

### 5. 生成指定用户的所有统计数据
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "user_id": {{testUserId}},
  "stats_type": "all"
}

### 6. 生成指定用户的周统计数据
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "user_id": {{testUserId}},
  "stats_type": "week"
}

### 7. 生成指定用户的月统计数据
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "user_id": {{testUserId}},
  "stats_type": "month"
}

### 8. 生成指定用户的年统计数据
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "user_id": {{testUserId}},
  "stats_type": "year"
}

### 9. 生成指定日期的统计数据
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "all",
  "target_date": "2024-01-15"
}

### 10. 生成指定用户指定日期的统计数据
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "user_id": {{testUserId}},
  "stats_type": "all",
  "target_date": "2024-01-15"
}

### 11. 权限测试 - 无token访问
POST {{baseUrl}}/admin/users/generate-meditation-stats
Content-Type: application/json

{
  "stats_type": "all"
}

### 12. 权限测试 - 无效token访问
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer invalid_token
Content-Type: application/json

{
  "stats_type": "all"
}

### 13. 参数测试 - 无效的统计类型
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "invalid_type"
}

### 14. 参数测试 - 无效的用户ID
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "user_id": 99999,
  "stats_type": "week"
}

### 15. 参数测试 - 无效的日期格式
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "month",
  "target_date": "invalid_date"
}

### 16. 验证生成的统计数据 - 检查周统计
GET {{baseUrl}}/api/user/meditation-stats?period_type=week
Authorization: Bearer {{testUserToken}}

### 17. 验证生成的统计数据 - 检查月统计
GET {{baseUrl}}/api/user/meditation-stats?period_type=month
Authorization: Bearer {{testUserToken}}

### 18. 验证生成的统计数据 - 检查年统计
GET {{baseUrl}}/api/user/meditation-stats?period_type=year
Authorization: Bearer {{testUserToken}}

### 19. 验证生成的统计数据 - 检查指定时间范围的周统计
GET {{baseUrl}}/api/user/meditation-stats?period_type=week&start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{testUserToken}}

### 20. 验证生成的统计数据 - 检查指定时间范围的月统计
GET {{baseUrl}}/api/user/meditation-stats?period_type=month&start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{testUserToken}}
