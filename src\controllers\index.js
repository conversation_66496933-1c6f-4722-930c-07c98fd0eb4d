import UploadController from './upload.js'
import OSSUploadController from './ossUpload.js'
import * as api from './api.js'
import * as auth from './auth.js'
import user from './user.js'
import meditation from './meditation.js'
import plan from './plan.js'
import plant from './plant.js'

export default {
  upload: UploadController.upload,
  ossUpload: OSSUploadController,
  api,
  auth,
  user,
  meditation,
  plan,
  plant
}
