/**
 * 测试周统计数据生成功能
 */

import moment from 'moment'
import { generateWeeklyStats, generateHistoricalWeeklyStats } from './src/scripts/aggregateStats.js'

async function testWeekStats() {
  console.log('=== 测试周统计数据生成功能 ===')
  
  try {
    // 1. 测试当前周统计生成
    console.log('\n1. 测试当前周统计生成...')
    const currentWeekResult = await generateWeeklyStats()
    console.log('当前周统计结果:', currentWeekResult ? (Array.isArray(currentWeekResult) ? `${currentWeekResult.length}条数据` : '1条数据') : '无数据')
    
    if (currentWeekResult && Array.isArray(currentWeekResult) && currentWeekResult.length > 0) {
      console.log('示例数据:', currentWeekResult[0])
    }
    
    // 2. 测试指定用户的周统计生成
    console.log('\n2. 测试指定用户的周统计生成...')
    const userWeekResult = await generateWeeklyStats(1) // 假设用户ID为1
    console.log('用户1的周统计结果:', userWeekResult ? '1条数据' : '无数据')
    
    if (userWeekResult) {
      console.log('用户数据:', userWeekResult)
    }
    
    // 3. 测试历史周统计生成
    console.log('\n3. 测试历史周统计生成...')
    const historicalResult = await generateHistoricalWeeklyStats(null, 2) // 生成过去2周的数据
    console.log('历史周统计结果:', historicalResult ? `${historicalResult.length}条数据` : '无数据')
    
    if (historicalResult && historicalResult.length > 0) {
      console.log('历史数据示例:')
      historicalResult.forEach((stat, index) => {
        console.log(`  第${index + 1}条: ${stat.period_date} 用户${stat.user_id} 任务${stat.tasks_completed}个`)
      })
    }
    
    // 4. 测试不同日期的周统计
    console.log('\n4. 测试不同日期的周统计...')
    const testDates = [
      '2024-01-15', // 一月中旬
      '2024-06-01', // 六月初
      '2024-12-25'  // 十二月底
    ]
    
    for (const testDate of testDates) {
      const dateResult = await generateWeeklyStats(null, testDate)
      const weekStart = moment(testDate).startOf('isoWeek').format('YYYY-MM-DD')
      const weekEnd = moment(testDate).endOf('isoWeek').format('YYYY-MM-DD')
      console.log(`  ${testDate} (${weekStart} 到 ${weekEnd}): ${dateResult ? (Array.isArray(dateResult) ? `${dateResult.length}条` : '1条') : '无数据'}`)
    }
    
    console.log('\n=== 测试完成 ===')
    
  } catch (error) {
    console.error('测试过程中出错:', error)
  }
  
  process.exit(0)
}

testWeekStats()
