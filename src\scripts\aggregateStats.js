/**
 * 用户冥想统计数据聚合脚本
 * 从天统计数据生成周、月、年统计数据
 */

import moment from 'moment'
import { UserMeditationStats } from '../models/associations.js'
import sequelize from 'sequelize'

const { Op } = sequelize

/**
 * 聚合指定用户的统计数据
 * @param {number} userId - 用户ID
 * @param {string} periodType - 周期类型 (week/month/year)
 * @param {string} startDate - 开始日期
 * @param {string} endDate - 结束日期
 */
async function aggregateUserStats(userId, periodType, startDate, endDate) {
  try {
    // 获取该时间范围内的天统计数据
    const dayStats = await UserMeditationStats.findAll({
      where: {
        user_id: userId,
        period_type: 'day',
        period_date: {
          [Op.between]: [startDate, endDate]
        }
      },
      order: [['period_date', 'ASC']]
    })

    if (dayStats.length === 0) {
      return null
    }

    // 聚合数据
    const aggregatedData = {
      user_id: userId,
      period_type: periodType,
      period_date: startDate, // 使用周期开始日期
      meditation_duration: dayStats.reduce((sum, stat) => sum + stat.meditation_duration, 0),
      energy_gained: dayStats.reduce((sum, stat) => sum + stat.energy_gained, 0),
      tasks_completed: dayStats.reduce((sum, stat) => sum + stat.tasks_completed, 0)
    }

    // 检查是否已存在该周期的统计数据
    const existingStat = await UserMeditationStats.findOne({
      where: {
        user_id: userId,
        period_type: periodType,
        period_date: startDate
      }
    })

    if (existingStat) {
      // 更新现有数据
      await existingStat.update(aggregatedData)
      return existingStat
    } else {
      // 创建新数据
      return await UserMeditationStats.create(aggregatedData)
    }
  } catch (error) {
    console.error(`聚合用户 ${userId} 的 ${periodType} 统计数据失败:`, error)
    throw error
  }
}

/**
 * 生成周统计数据
 * @param {number} userId - 用户ID，如果不提供则为所有用户生成
 * @param {string} targetDate - 目标日期，默认为当前日期
 */
async function generateWeeklyStats(userId = null, targetDate = null) {
  const date = moment(targetDate || new Date())
  // 使用 ISO 周（周一开始）
  const weekStart = date.clone().startOf('isoWeek').format('YYYY-MM-DD')
  const weekEnd = date.clone().endOf('isoWeek').format('YYYY-MM-DD')

  console.log(`生成周统计数据: ${weekStart} 到 ${weekEnd}`)

  try {
    if (userId) {
      // 为指定用户生成
      return await aggregateUserStats(userId, 'week', weekStart, weekEnd)
    } else {
      // 为所有有天统计数据的用户生成
      const usersWithDayStats = await UserMeditationStats.findAll({
        attributes: ['user_id'],
        where: {
          period_type: 'day',
          period_date: {
            [Op.between]: [weekStart, weekEnd]
          }
        },
        group: ['user_id']
      })

      const results = []
      for (const userStat of usersWithDayStats) {
        try {
          const result = await aggregateUserStats(userStat.user_id, 'week', weekStart, weekEnd)
          if (result) {
            results.push(result)
          }
        } catch (error) {
          console.error(`为用户 ${userStat.user_id} 生成周统计失败:`, error)
        }
      }

      return results
    }
  } catch (error) {
    console.error('生成周统计数据失败:', error)
    throw error
  }
}

/**
 * 生成月统计数据
 * @param {number} userId - 用户ID，如果不提供则为所有用户生成
 * @param {string} targetDate - 目标日期，默认为当前日期
 */
async function generateMonthlyStats(userId = null, targetDate = null) {
  const date = moment(targetDate || new Date())
  const monthStart = date.clone().startOf('month').format('YYYY-MM-DD')
  const monthEnd = date.clone().endOf('month').format('YYYY-MM-DD')

  console.log(`生成月统计数据: ${monthStart} 到 ${monthEnd}`)

  try {
    if (userId) {
      // 为指定用户生成
      return await aggregateUserStats(userId, 'month', monthStart, monthEnd)
    } else {
      // 为所有有天统计数据的用户生成
      const usersWithDayStats = await UserMeditationStats.findAll({
        attributes: ['user_id'],
        where: {
          period_type: 'day',
          period_date: {
            [Op.between]: [monthStart, monthEnd]
          }
        },
        group: ['user_id']
      })

      const results = []
      for (const userStat of usersWithDayStats) {
        try {
          const result = await aggregateUserStats(userStat.user_id, 'month', monthStart, monthEnd)
          if (result) {
            results.push(result)
          }
        } catch (error) {
          console.error(`为用户 ${userStat.user_id} 生成月统计失败:`, error)
        }
      }

      return results
    }
  } catch (error) {
    console.error('生成月统计数据失败:', error)
    throw error
  }
}

/**
 * 生成年统计数据
 * @param {number} userId - 用户ID，如果不提供则为所有用户生成
 * @param {string} targetDate - 目标日期，默认为当前日期
 */
async function generateYearlyStats(userId = null, targetDate = null) {
  const date = moment(targetDate || new Date())
  const yearStart = date.clone().startOf('year').format('YYYY-MM-DD')
  const yearEnd = date.clone().endOf('year').format('YYYY-MM-DD')

  console.log(`生成年统计数据: ${yearStart} 到 ${yearEnd}`)

  try {
    if (userId) {
      // 为指定用户生成
      return await aggregateUserStats(userId, 'year', yearStart, yearEnd)
    } else {
      // 为所有有天统计数据的用户生成
      const usersWithDayStats = await UserMeditationStats.findAll({
        attributes: ['user_id'],
        where: {
          period_type: 'day',
          period_date: {
            [Op.between]: [yearStart, yearEnd]
          }
        },
        group: ['user_id']
      })

      const results = []
      for (const userStat of usersWithDayStats) {
        try {
          const result = await aggregateUserStats(userStat.user_id, 'year', yearStart, yearEnd)
          if (result) {
            results.push(result)
          }
        } catch (error) {
          console.error(`为用户 ${userStat.user_id} 生成年统计失败:`, error)
        }
      }

      return results
    }
  } catch (error) {
    console.error('生成年统计数据失败:', error)
    throw error
  }
}

/**
 * 生成历史周统计数据
 * @param {number} userId - 用户ID，如果不提供则为所有用户生成
 * @param {number} weeksBack - 向前生成多少周的数据，默认4周
 */
async function generateHistoricalWeeklyStats(userId = null, weeksBack = 4) {
  console.log(`生成过去${weeksBack}周的统计数据...`)

  const results = []

  for (let i = 0; i < weeksBack; i++) {
    const targetDate = moment().subtract(i, 'weeks').format('YYYY-MM-DD')
    console.log(`生成第${i}周前的数据 (${targetDate})...`)

    try {
      const weekResult = await generateWeeklyStats(userId, targetDate)
      if (weekResult) {
        if (Array.isArray(weekResult)) {
          results.push(...weekResult)
        } else {
          results.push(weekResult)
        }
      }
    } catch (error) {
      console.error(`生成第${i}周前的数据失败:`, error)
    }
  }

  return results
}

/**
 * 批量生成所有类型的统计数据
 * @param {number} userId - 用户ID，如果不提供则为所有用户生成
 * @param {string} targetDate - 目标日期，默认为当前日期
 * @param {boolean} includeHistorical - 是否包含历史数据，默认true
 */
async function generateAllStats(userId = null, targetDate = null, includeHistorical = true) {
  console.log('开始生成统计数据...')

  try {
    const results = {
      weekly: [],
      monthly: [],
      yearly: []
    }

    // 生成周统计
    console.log('生成周统计数据...')
    if (includeHistorical && !targetDate) {
      // 如果没有指定日期且需要历史数据，生成过去几周的数据
      results.weekly = await generateHistoricalWeeklyStats(userId, 4)
    } else {
      // 只生成指定日期的周统计
      const weekResult = await generateWeeklyStats(userId, targetDate)
      if (weekResult) {
        results.weekly = Array.isArray(weekResult) ? weekResult : [weekResult]
      }
    }

    // 生成月统计
    console.log('生成月统计数据...')
    const monthResult = await generateMonthlyStats(userId, targetDate)
    if (monthResult) {
      results.monthly = Array.isArray(monthResult) ? monthResult : [monthResult]
    }

    // 生成年统计
    console.log('生成年统计数据...')
    const yearResult = await generateYearlyStats(userId, targetDate)
    if (yearResult) {
      results.yearly = Array.isArray(yearResult) ? yearResult : [yearResult]
    }

    console.log('统计数据生成完成!')
    console.log(`周统计: ${results.weekly.length} 条`)
    console.log(`月统计: ${results.monthly.length} 条`)
    console.log(`年统计: ${results.yearly.length} 条`)

    return results
  } catch (error) {
    console.error('生成统计数据失败:', error)
    throw error
  }
}

// 如果直接运行此脚本
if (import.meta.url === `file://${process.argv[1]}`) {
  const args = process.argv.slice(2)
  
  let userId = null
  let targetDate = null
  let statsType = 'all'

  // 解析命令行参数
  for (let i = 0; i < args.length; i++) {
    if (args[i] === '--user' && args[i + 1]) {
      userId = parseInt(args[i + 1])
      i++
    } else if (args[i] === '--date' && args[i + 1]) {
      targetDate = args[i + 1]
      i++
    } else if (args[i] === '--type' && args[i + 1]) {
      statsType = args[i + 1]
      i++
    }
  }

  // 执行相应的统计生成
  let promise
  switch (statsType) {
    case 'week':
      promise = generateWeeklyStats(userId, targetDate)
      break
    case 'month':
      promise = generateMonthlyStats(userId, targetDate)
      break
    case 'year':
      promise = generateYearlyStats(userId, targetDate)
      break
    default:
      promise = generateAllStats(userId, targetDate)
  }

  promise
    .then((result) => {
      console.log('执行完成:', result ? '成功' : '无数据')
      process.exit(0)
    })
    .catch((error) => {
      console.error('执行失败:', error)
      process.exit(1)
    })
}

export {
  generateWeeklyStats,
  generateMonthlyStats,
  generateYearlyStats,
  generateAllStats,
  generateHistoricalWeeklyStats,
  aggregateUserStats
}
