import User from '../models/User.js'
import Plant from '../models/Plant.js'
import UserMeditationStats from '../models/UserMeditationStats.js'
import UserFavorite from '../models/UserFavorite.js'
import sequelize from 'sequelize'
import { parsePaginationParams, formatPaginationResponse, updateUserStreakDays } from '../tool/Common.js'
import { updateAllUsersStreakDays, updateSingleUserStreakDays } from '../scripts/updateStreakDays.js'
import { generateAllStats, generateWeeklyStats, generateMonthlyStats, generateYearlyStats, generateHistoricalWeeklyStats } from '../scripts/aggregateStats.js'

const { Op } = sequelize

export default class AdminUserController {
  /**
   * @swagger
   * /admin/users:
   *   get:
   *     tags:
   *       - 用户管理
   *     summary: 获取用户列表
   *     description: 获取所有注册用户列表，支持搜索和筛选
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: query
   *         name: page
   *         schema:
   *           type: integer
   *           default: 1
   *         description: 页码
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           default: 10
   *         description: 每页数量
   *       - in: query
   *         name: search
   *         schema:
   *           type: string
   *         description: 搜索关键词（昵称、openid）
   *       - in: query
   *         name: meditation_level
   *         schema:
   *           type: integer
   *         description: 冥想等级筛选
   *       - in: query
   *         name: start_date
   *         schema:
   *           type: string
   *           format: date
   *         description: 注册开始日期
   *       - in: query
   *         name: end_date
   *         schema:
   *           type: string
   *           format: date
   *         description: 注册结束日期
   *     responses:
   *       200:
   *         description: 获取成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 data:
   *                   type: object
   *                   properties:
   *                     list:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: integer
   *                           openid:
   *                             type: string
   *                           nickname:
   *                             type: string
   *                           avatar_url:
   *                             type: string
   *                           meditation_level:
   *                             type: integer
   *                           streak_days:
   *                             type: integer
   *                           plant_count:
   *                             type: integer
   *                           created_at:
   *                             type: string
   *                             format: date-time
   *                     pagination:
   *                       type: object
   *                       properties:
   *                         current_page:
   *                           type: integer
   *                         per_page:
   *                           type: integer
   *                         total:
   *                           type: integer
   *                         total_pages:
   *                           type: integer
   */
  static async getUserList(ctx) {
    const { search, meditation_level, start_date, end_date } = ctx.query

    try {
      const { pageNum, pageSize, offset } = parsePaginationParams(ctx.query)
      const whereCondition = {}

      // 搜索条件
      if (search) {
        whereCondition[Op.or] = [
          { nickname: { [Op.like]: `%${search}%` } },
          { openid: { [Op.like]: `%${search}%` } }
        ]
      }

      // 冥想等级筛选
      if (meditation_level) {
        whereCondition.meditation_level = meditation_level
      }

      // 注册时间筛选
      if (start_date && end_date) {
        whereCondition.created_at = {
          [Op.between]: [start_date, end_date]
        }
      } else if (start_date) {
        whereCondition.created_at = {
          [Op.gte]: start_date
        }
      } else if (end_date) {
        whereCondition.created_at = {
          [Op.lte]: end_date
        }
      }

      const users = await User.findAndCountAll({
        where: whereCondition,
        limit: pageSize,
        offset: offset,
        order: [['created_at', 'DESC']],
        attributes: ['id', 'openid', 'nickname', 'avatar_url', 'meditation_level', 'streak_days', 'created_at', 'updated_at']
      })

      // 获取每个用户的多肉数量
      const userIds = users.rows.map(user => user.id)
      const plantCounts = await Plant.findAll({
        where: { user_id: { [Op.in]: userIds } },
        attributes: ['user_id', [Plant.sequelize.fn('COUNT', Plant.sequelize.col('id')), 'count']],
        group: ['user_id'],
        raw: true
      })

      const plantCountMap = {}
      plantCounts.forEach(item => {
        plantCountMap[item.user_id] = parseInt(item.count)
      })

      // 组装返回数据
      const userList = users.rows.map(user => ({
        ...user.toJSON(),
        plant_count: plantCountMap[user.id] || 0
      }))

      ctx.body = {
        code: 200,
        data: formatPaginationResponse(userList, users.count, pageNum, pageSize)
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取用户列表失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/users/{id}:
   *   get:
   *     tags:
   *       - 用户管理
   *     summary: 获取用户详情
   *     description: 获取指定用户的详细信息，包括统计数据
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     responses:
   *       200:
   *         description: 获取成功
   *       404:
   *         description: 用户不存在
   */
  static async getUserDetail(ctx) {
    const { id } = ctx.params

    try {
      const user = await User.findByPk(id)
      if (!user) {
        ctx.body = {
          code: 404,
          message: '用户不存在'
        }
        return
      }

      // 更新用户连续天数
      let currentStreakDays = user.streak_days
      try {
        currentStreakDays = await updateUserStreakDays(id)
      } catch (error) {
        console.error('更新连续天数失败:', error)
        // 如果更新失败，使用数据库中的值
      }

      // 获取用户多肉数量
      const plantCount = await Plant.count({ where: { user_id: id } })

      // 获取用户收藏数量
      const favoriteCount = await UserFavorite.count({ where: { user_id: id } })

      // 获取用户冥想统计（最近30天）
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const meditationStats = await UserMeditationStats.findAll({
        where: {
          user_id: id,
          period_type: 'day',
          period_date: {
            [Op.gte]: thirtyDaysAgo
          }
        },
        order: [['period_date', 'DESC']]
      })

      // 构建用户数据，使用实时计算的连续天数
      const userData = {
        ...user.toJSON(),
        streak_days: currentStreakDays
      }

      ctx.body = {
        code: 200,
        data: {
          user: userData,
          statistics: {
            plant_count: plantCount,
            favorite_count: favoriteCount,
            meditation_stats: meditationStats
          }
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取用户详情失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/users/{id}/level:
   *   put:
   *     tags:
   *       - 用户管理
   *     summary: 更新用户等级
   *     description: 手动调整用户的冥想等级
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - meditation_level
   *             properties:
   *               meditation_level:
   *                 type: integer
   *                 minimum: 1
   *                 description: 新的冥想等级
   *               reason:
   *                 type: string
   *                 description: 调整原因
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 用户不存在
   */
  static async updateUserLevel(ctx) {
    const { id } = ctx.params
    const { meditation_level, reason } = ctx.request.body

    if (!meditation_level || meditation_level < 1) {
      ctx.body = {
        code: 400,
        message: '冥想等级必须大于0'
      }
      return
    }

    try {
      const user = await User.findByPk(id)
      if (!user) {
        ctx.body = {
          code: 404,
          message: '用户不存在'
        }
        return
      }

      const oldLevel = user.meditation_level
      await user.update({ meditation_level })

      // 记录操作日志（这里可以扩展为专门的操作日志表）
      console.log(`管理员 ${ctx.state.admin.username} 将用户 ${user.nickname}(${user.id}) 的等级从 ${oldLevel} 调整为 ${meditation_level}，原因：${reason || '无'}`)

      ctx.body = {
        code: 200,
        message: '用户等级更新成功',
        data: {
          user_id: user.id,
          old_level: oldLevel,
          new_level: meditation_level,
          updated_at: new Date()
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新用户等级失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/users/statistics:
   *   get:
   *     tags:
   *       - 用户管理
   *     summary: 获取用户统计数据
   *     description: 获取用户总体统计数据，包括等级分布、注册趋势等
   *     security:
   *       - AdminBearer: []
   *     responses:
   *       200:
   *         description: 获取成功
   */
  static async getUserStatistics(ctx) {
    try {
      // 用户总数
      const totalUsers = await User.count()

      // 等级分布
      const levelDistribution = await User.findAll({
        attributes: [
          'meditation_level',
          [User.sequelize.fn('COUNT', User.sequelize.col('id')), 'count']
        ],
        group: ['meditation_level'],
        order: [['meditation_level', 'ASC']],
        raw: true
      })

      // 最近7天注册用户数
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)

      const recentRegistrations = await User.findAll({
        attributes: [
          [User.sequelize.fn('DATE', User.sequelize.col('created_at')), 'date'],
          [User.sequelize.fn('COUNT', User.sequelize.col('id')), 'count']
        ],
        where: {
          created_at: {
            [Op.gte]: sevenDaysAgo
          }
        },
        group: [User.sequelize.fn('DATE', User.sequelize.col('created_at'))],
        order: [[User.sequelize.fn('DATE', User.sequelize.col('created_at')), 'ASC']],
        raw: true
      })

      // 活跃用户数（最近7天有冥想记录的用户）
      const activeUsers = await UserMeditationStats.findAll({
        attributes: [
          [UserMeditationStats.sequelize.fn('COUNT', UserMeditationStats.sequelize.fn('DISTINCT', UserMeditationStats.sequelize.col('user_id'))), 'count']
        ],
        where: {
          period_date: {
            [Op.gte]: sevenDaysAgo
          }
        }, 
        raw: true
      })

      ctx.body = {
        code: 200,
        data: {
          total_users: totalUsers,
          level_distribution: levelDistribution,
          recent_registrations: recentRegistrations,
          active_users: activeUsers[0]?.count || 0
        }
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '获取用户统计数据失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/users/update-streak-days:
   *   post:
   *     tags:
   *       - 用户管理
   *     summary: 批量更新用户连续天数
   *     description: 批量更新所有用户的连续坚持天数
   *     security:
   *       - AdminBearer: []
   *     responses:
   *       200:
   *         description: 更新成功
   */
  static async updateAllStreakDays(ctx) {
    try {
      console.log('管理员触发批量更新用户连续天数')
      const result = await updateAllUsersStreakDays()

      ctx.body = {
        code: 200,
        message: '批量更新连续天数完成',
        data: result
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '批量更新连续天数失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /admin/users/{id}/update-streak-days:
   *   post:
   *     tags:
   *       - 用户管理
   *     summary: 更新指定用户连续天数
   *     description: 更新指定用户的连续坚持天数
   *     security:
   *       - AdminBearer: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: integer
   *         description: 用户ID
   *     responses:
   *       200:
   *         description: 更新成功
   *       404:
   *         description: 用户不存在
   */
  static async updateSingleStreakDays(ctx) {
    const { id } = ctx.params

    try {
      console.log(`管理员触发更新用户 ${id} 的连续天数`)
      const result = await updateSingleUserStreakDays(parseInt(id))

      if (!result) {
        ctx.body = {
          code: 404,
          message: '用户不存在'
        }
        return
      }

      ctx.body = {
        code: 200,
        message: '更新连续天数完成',
        data: result
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '更新连续天数失败',
        error: error.message
      }
    }
  }

  /**
   * 生成用户冥想统计聚合数据
   */
  static async generateMeditationStats (ctx) {
    const { user_id, stats_type = 'all', target_date, weeks_back = 4 } = ctx.request.body

    try {
      let result

      switch (stats_type) {
        case 'week':
          result = await generateWeeklyStats(user_id, target_date)
          break
        case 'week_historical':
          result = await generateHistoricalWeeklyStats(user_id, weeks_back)
          break
        case 'month':
          result = await generateMonthlyStats(user_id, target_date)
          break
        case 'year':
          result = await generateYearlyStats(user_id, target_date)
          break
        default:
          result = await generateAllStats(user_id, target_date)
      }

      ctx.body = {
        code: 200,
        message: '统计数据生成成功',
        data: result
      }
    } catch (error) {
      ctx.body = {
        code: 500,
        message: '生成统计数据失败',
        error: error.message
      }
    }
  }
}
