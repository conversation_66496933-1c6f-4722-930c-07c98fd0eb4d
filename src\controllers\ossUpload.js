import OSS from 'ali-oss'
import { AliOSS as OSSConfig } from '../config.js'
import path from 'path'
import fs from 'fs'
import { v4 as uuidv4 } from 'uuid'

/**
 * 阿里云OSS上传控制器
 */
export default class OSSUploadController {
  /**
   * 初始化OSS客户端
   */
  static getOSSClient() {
    return new OSS({
      region: OSSConfig.region,
      accessKeyId: OSSConfig.accessKeyId,
      accessKeySecret: OSSConfig.accessKeySecret,
      bucket: OSSConfig.bucket
    })
  }

  /**
   * 生成文件名
   * @param {string} originalName 原始文件名
   * @param {string} folder 文件夹前缀
   * @returns {string} 新文件名
   */
  static generateFileName(originalName, folder = '') {
    const ext = path.extname(originalName)
    const timestamp = Date.now()
    const uuid = uuidv4().replace(/-/g, '').substring(0, 8)
    const fileName = `${timestamp}_${uuid}${ext}`
    
    return folder ? `${folder}${fileName}` : fileName
  }

  /**
   * 验证文件类型
   * @param {string} mimetype 文件MIME类型
   * @param {Array} allowedTypes 允许的文件类型
   * @returns {boolean} 是否允许
   */
  static validateFileType(mimetype, allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp']) {
    return allowedTypes.includes(mimetype)
  }

  /**
   * 验证文件大小
   * @param {number} size 文件大小（字节）
   * @param {number} maxSize 最大文件大小（字节），默认5MB
   * @returns {boolean} 是否允许
   */
  static validateFileSize(size, maxSize = 5 * 1024 * 1024) {
    return size <= maxSize
  }

  /**
   * @swagger
   * /api/oss/upload:
   *   post:
   *     tags:
   *       - OSS文件上传
   *     summary: 上传图片到阿里云OSS
   *     description: 上传单个图片文件到阿里云OSS存储
   *     security:
   *       - Bearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         multipart/form-data:
   *           schema:
   *             type: object
   *             properties:
   *               file:
   *                 type: string
   *                 format: binary
   *                 description: 要上传的图片文件
   *               folder:
   *                 type: string
   *                 description: 上传到的文件夹（可选）
   *                 example: "avatars/"
   *     responses:
   *       200:
   *         description: 上传成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "上传成功"
   *                 data:
   *                   type: object
   *                   properties:
   *                     url:
   *                       type: string
   *                       description: 文件访问URL
   *                       example: "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/meditation-app/avatars/1640995200000_abc12345.jpg"
   *                     name:
   *                       type: string
   *                       description: 文件名
   *                       example: "meditation-app/avatars/1640995200000_abc12345.jpg"
   *                     size:
   *                       type: integer
   *                       description: 文件大小（字节）
   *                       example: 102400
   *       400:
   *         description: 请求错误
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 400
   *                 message:
   *                   type: string
   *                   example: "未选择文件"
   *       500:
   *         description: 服务器错误
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 500
   *                 message:
   *                   type: string
   *                   example: "上传失败"
   *                 error:
   *                   type: string
   *                   description: 错误详情
   */
  static async uploadImage(ctx) {
    try {
      const files = ctx.request.body.files
      const { folder } = ctx.request.body.fields || {}

      // 检查是否有文件上传
      if (!files || !files.file) {
        ctx.body = {
          code: 400,
          message: '未选择文件'
        }
        return
      }

      const file = files.file
      
      // 验证文件类型
      if (!OSSUploadController.validateFileType(file.type)) {
        ctx.body = {
          code: 400,
          message: '不支持的文件类型，仅支持 jpg、jpeg、png、gif、webp 格式'
        }
        return
      }

      // 验证文件大小
      if (!OSSUploadController.validateFileSize(file.size)) {
        ctx.body = {
          code: 400,
          message: '文件大小超过限制，最大支持5MB'
        }
        return
      }

      // 生成文件名
      const uploadFolder = folder || OSSConfig.folder
      const fileName = OSSUploadController.generateFileName(file.name, uploadFolder)

      // 初始化OSS客户端
      const client = OSSUploadController.getOSSClient()

      // 上传文件到OSS
      const result = await client.put(fileName, file.path)

      // 删除临时文件
      if (fs.existsSync(file.path)) {
        fs.unlinkSync(file.path)
      }

      // 构建访问URL
      let fileUrl = result.url
      if (OSSConfig.customDomain) {
        fileUrl = `${OSSConfig.customDomain}/${fileName}`
      }

      ctx.body = {
        code: 200,
        message: '上传成功',
        data: {
          url: fileUrl,
          name: fileName,
          size: file.size
        }
      }

    } catch (error) {
      console.error('OSS上传失败:', error)
      ctx.body = {
        code: 500,
        message: '上传失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /api/oss/upload/multiple:
   *   post:
   *     tags:
   *       - OSS文件上传
   *     summary: 批量上传图片到阿里云OSS
   *     description: 批量上传多个图片文件到阿里云OSS存储
   *     security:
   *       - Bearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         multipart/form-data:
   *           schema:
   *             type: object
   *             properties:
   *               files:
   *                 type: array
   *                 items:
   *                   type: string
   *                   format: binary
   *                 description: 要上传的图片文件数组
   *               folder:
   *                 type: string
   *                 description: 上传到的文件夹（可选）
   *                 example: "gallery/"
   *     responses:
   *       200:
   *         description: 上传成功
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 code:
   *                   type: integer
   *                   example: 200
   *                 message:
   *                   type: string
   *                   example: "批量上传完成"
   *                 data:
   *                   type: object
   *                   properties:
   *                     success:
   *                       type: array
   *                       description: 上传成功的文件列表
   *                       items:
   *                         type: object
   *                         properties:
   *                           url:
   *                             type: string
   *                           name:
   *                             type: string
   *                           size:
   *                             type: integer
   *                     failed:
   *                       type: array
   *                       description: 上传失败的文件列表
   *                       items:
   *                         type: object
   *                         properties:
   *                           name:
   *                             type: string
   *                           error:
   *                             type: string
   */
  static async uploadMultipleImages(ctx) {
    try {
      const files = ctx.request.body.files
      const { folder } = ctx.request.body.fields || {}

      if (!files) {
        ctx.body = {
          code: 400,
          message: '未选择文件'
        }
        return
      }

      // 处理单个文件或多个文件的情况
      const fileList = Array.isArray(files.files) ? files.files : [files.files || files.file]
      
      if (!fileList || fileList.length === 0 || !fileList[0]) {
        ctx.body = {
          code: 400,
          message: '未选择文件'
        }
        return
      }

      const client = OSSUploadController.getOSSClient()
      const uploadFolder = folder || OSSConfig.folder
      const results = {
        success: [],
        failed: []
      }

      // 批量上传文件
      for (const file of fileList) {
        try {
          // 验证文件类型和大小
          if (!OSSUploadController.validateFileType(file.type)) {
            results.failed.push({
              name: file.name,
              error: '不支持的文件类型'
            })
            continue
          }

          if (!OSSUploadController.validateFileSize(file.size)) {
            results.failed.push({
              name: file.name,
              error: '文件大小超过限制'
            })
            continue
          }

          // 生成文件名并上传
          const fileName = OSSUploadController.generateFileName(file.name, uploadFolder)
          const result = await client.put(fileName, file.path)

          // 删除临时文件
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path)
          }

          // 构建访问URL
          let fileUrl = result.url
          if (OSSConfig.customDomain) {
            fileUrl = `${OSSConfig.customDomain}/${fileName}`
          }

          results.success.push({
            url: fileUrl,
            name: fileName,
            size: file.size
          })

        } catch (error) {
          results.failed.push({
            name: file.name,
            error: error.message
          })
        }
      }

      ctx.body = {
        code: 200,
        message: '批量上传完成',
        data: results
      }

    } catch (error) {
      console.error('批量上传失败:', error)
      ctx.body = {
        code: 500,
        message: '批量上传失败',
        error: error.message
      }
    }
  }

  /**
   * @swagger
   * /api/oss/delete:
   *   delete:
   *     tags:
   *       - OSS文件上传
   *     summary: 删除OSS中的文件
   *     description: 从阿里云OSS中删除指定的文件
   *     security:
   *       - Bearer: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               fileName:
   *                 type: string
   *                 description: 要删除的文件名（OSS中的完整路径）
   *                 example: "meditation-app/avatars/1640995200000_abc12345.jpg"
   *     responses:
   *       200:
   *         description: 删除成功
   *       400:
   *         description: 请求错误
   *       500:
   *         description: 服务器错误
   */
  static async deleteFile(ctx) {
    try {
      const { fileName } = ctx.request.body

      if (!fileName) {
        ctx.body = {
          code: 400,
          message: '文件名不能为空'
        }
        return
      }

      const client = OSSUploadController.getOSSClient()
      await client.delete(fileName)

      ctx.body = {
        code: 200,
        message: '删除成功'
      }

    } catch (error) {
      console.error('删除文件失败:', error)
      ctx.body = {
        code: 500,
        message: '删除失败',
        error: error.message
      }
    }
  }
}
