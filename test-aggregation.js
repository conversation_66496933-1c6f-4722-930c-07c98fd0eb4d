/**
 * 测试统计数据聚合功能
 */

import { generateAllStats } from './src/scripts/aggregateStats.js'

async function testAggregation() {
  console.log('开始测试统计数据聚合功能...')
  
  try {
    // 为所有用户生成统计数据
    const result = await generateAllStats()
    
    console.log('测试结果:')
    console.log('周统计数据:', Array.isArray(result.weekly) ? result.weekly.length : (result.weekly ? 1 : 0), '条')
    console.log('月统计数据:', Array.isArray(result.monthly) ? result.monthly.length : (result.monthly ? 1 : 0), '条')
    console.log('年统计数据:', Array.isArray(result.yearly) ? result.yearly.length : (result.yearly ? 1 : 0), '条')
    
    if (result.weekly && result.weekly.length > 0) {
      console.log('周统计示例:', result.weekly[0])
    }
    
    if (result.monthly && result.monthly.length > 0) {
      console.log('月统计示例:', result.monthly[0])
    }
    
    if (result.yearly && result.yearly.length > 0) {
      console.log('年统计示例:', result.yearly[0])
    }
    
    console.log('测试完成!')
    
  } catch (error) {
    console.error('测试失败:', error)
  }
  
  process.exit(0)
}

testAggregation()
