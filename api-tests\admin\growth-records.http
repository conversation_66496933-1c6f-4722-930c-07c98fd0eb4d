### 成长记录管理接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取管理员token
< ../auth/admin-auth.http

@baseUrl = http://localhost:3004
@adminToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJ1c2VybmFtZSI6ImFkbWluIiwicm9sZSI6InN1cGVyX2FkbWluIiwiaXNBZG1pbiI6dHJ1ZSwiaWF0IjoxNzU2MzU3ODE3LCJleHAiOjE3NTY0NDQyMTd9.wlcslhq6_nlAtv8CXEQpmeCyUOXpcKqniYbUKY72PJM
@defaultPage = 1
@defaultLimit = 10
@testUserId = 1

### 1. 获取成长记录列表 - 默认分页
GET {{baseUrl}}/admin/growth-records
Authorization: Bearer {{adminToken}}

### 2. 获取成长记录列表 - 指定分页
GET {{baseUrl}}/admin/growth-records?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{adminToken}}

### 3. 获取成长记录列表 - 搜索功能
GET {{baseUrl}}/admin/growth-records?search=测试&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 4. 获取成长记录列表 - 按用户等级筛选
GET {{baseUrl}}/admin/growth-records?level=2&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 5. 获取成长记录列表 - 按多肉等级筛选
GET {{baseUrl}}/admin/growth-records?plant_level=3&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 6. 获取成长记录列表 - 按健康状态筛选
GET {{baseUrl}}/admin/growth-records?health_status=healthy&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 7. 获取成长记录列表 - 按活跃度筛选
GET {{baseUrl}}/admin/growth-records?activity_level=high&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 8. 获取成长记录列表 - 复合筛选
GET {{baseUrl}}/admin/growth-records?search=用户&level=2&plant_level=3&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 9. 获取用户成长记录详情 - 正常用户
GET {{baseUrl}}/admin/growth-records/{{testUserId}}
Authorization: Bearer {{adminToken}}

### 10. 获取用户成长记录详情 - 不存在的用户
GET {{baseUrl}}/admin/growth-records/99999
Authorization: Bearer {{adminToken}}

### 11. 获取用户成长记录详情 - 包含成就信息
GET {{baseUrl}}/admin/growth-records/{{testUserId}}?include_achievements=true
Authorization: Bearer {{adminToken}}

### 12. 获取用户成长记录详情 - 包含多肉详情
GET {{baseUrl}}/admin/growth-records/{{testUserId}}?include_plants=true
Authorization: Bearer {{adminToken}}

### 13. 更新用户等级 - 正常升级
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 3,
  "reason": "管理员手动调整等级"
}

### 14. 更新用户等级 - 降级处理
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 1,
  "reason": "降级处理"
}

### 15. 更新用户等级 - 升级到最高等级
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 5,
  "reason": "升级到最高等级"
}

### 16. 更新用户等级 - 无效等级
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 0,
  "reason": "测试无效等级"
}

### 17. 更新用户等级 - 超出范围等级
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 100,
  "reason": "测试超出范围等级"
}

### 18. 更新用户等级 - 缺少原因
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 2
}

### 19. 更新多肉能量值 - 增加能量
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/plants/{{testPlantId}}/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": 100,
  "reason": "管理员奖励能量"
}

### 20. 更新多肉能量值 - 减少能量
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/plants/{{testPlantId}}/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": -50,
  "reason": "管理员扣除能量"
}

### 21. 更新多肉能量值 - 大幅增加能量
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/plants/{{testPlantId}}/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": 500,
  "reason": "大幅奖励测试"
}

### 22. 更新多肉能量值 - 尝试负能量
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/plants/{{testPlantId}}/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": -10000,
  "reason": "负能量测试"
}

### 23. 更新多肉能量值 - 多肉不存在
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/plants/99999/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": 50,
  "reason": "测试多肉不存在"
}

### 24. 更新多肉能量值 - 用户不存在
PUT {{baseUrl}}/admin/growth-records/99999/plants/{{testPlantId}}/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": 50,
  "reason": "测试用户不存在"
}

### 25. 更新多肉能量值 - 缺少原因
PUT {{baseUrl}}/admin/growth-records/{{testUserId}}/plants/{{testPlantId}}/update-energy
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "energy_change": 50
}

### 26. 获取成长记录统计数据 - 基础统计
GET {{baseUrl}}/admin/growth-records/statistics
Authorization: Bearer {{adminToken}}

### 27. 获取成长记录统计数据 - 按时间范围
GET {{baseUrl}}/admin/growth-records/statistics?start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{adminToken}}

### 28. 获取成长记录统计数据 - 按等级分组
GET {{baseUrl}}/admin/growth-records/statistics?group_by=level
Authorization: Bearer {{adminToken}}

### 29. 获取成长记录统计数据 - 按多肉等级分组
GET {{baseUrl}}/admin/growth-records/statistics?group_by=plant_level
Authorization: Bearer {{adminToken}}

### 30. 获取成长记录统计数据 - 详细统计
GET {{baseUrl}}/admin/growth-records/statistics?include_details=true
Authorization: Bearer {{adminToken}}

### 31. 批量操作测试 - 连续更新多个用户等级
PUT {{baseUrl}}/admin/growth-records/1/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 3,
  "reason": "批量调整-用户1"
}

###
PUT {{baseUrl}}/admin/growth-records/2/update-level
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "level": 4,
  "reason": "批量调整-用户2"
}

### 32. 分页测试 - 小分页
GET {{baseUrl}}/admin/growth-records?page=1&limit=5
Authorization: Bearer {{adminToken}}

### 33. 分页测试 - 大分页
GET {{baseUrl}}/admin/growth-records?page=1&limit=50
Authorization: Bearer {{adminToken}}

### 34. 分页测试 - 超出范围页码
GET {{baseUrl}}/admin/growth-records?page=99999&limit=10
Authorization: Bearer {{adminToken}}

### 35. 排序测试 - 按等级排序
GET {{baseUrl}}/admin/growth-records?sort=level&order=desc&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 36. 排序测试 - 按最后活跃时间排序
GET {{baseUrl}}/admin/growth-records?sort=last_active&order=desc&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 37. 健康状态测试 - 查看不同健康状态用户
GET {{baseUrl}}/admin/growth-records?health_status=warning&page=1&limit=10
Authorization: Bearer {{adminToken}}

###
GET {{baseUrl}}/admin/growth-records?health_status=critical&page=1&limit=10
Authorization: Bearer {{adminToken}}

### 38. 成就系统测试 - 查看用户成就详情
GET {{baseUrl}}/admin/growth-records/{{testUserId}}?include_achievements=true&include_achievement_progress=true
Authorization: Bearer {{adminToken}}

### 39. 能量管理测试 - 查看能量变化历史
GET {{baseUrl}}/admin/growth-records/{{testUserId}}/energy-history?page=1&limit=20
Authorization: Bearer {{adminToken}}

### 40. 权限测试 - 无token访问
GET {{baseUrl}}/admin/growth-records

### 41. 权限测试 - 无效token访问
GET {{baseUrl}}/admin/growth-records
Authorization: Bearer invalid_token

### 42. 性能测试 - 大量数据查询
GET {{baseUrl}}/admin/growth-records?page=1&limit=100
Authorization: Bearer {{adminToken}}

### 43. 数据导出测试 - 导出成长记录
GET {{baseUrl}}/admin/growth-records/export?format=csv&page=1&limit=100
Authorization: Bearer {{adminToken}}

### 44. 数据导出测试 - 导出统计数据
GET {{baseUrl}}/admin/growth-records/statistics/export?format=json
Authorization: Bearer {{adminToken}}

### 45. 实时数据测试 - 获取实时统计
GET {{baseUrl}}/admin/growth-records/real-time-stats
Authorization: Bearer {{adminToken}}
