-- 为 plans 表添加新字段以支持循环计划功能
-- 执行日期: 2025-08-29

ALTER TABLE plans 
ADD COLUMN plan_type ENUM('once', 'recurring') DEFAULT 'once' COMMENT '计划类型：once=一次性，recurring=循环',
ADD COLUMN cycle_duration ENUM('week', 'month', 'quarter') DEFAULT NULL COMMENT '循环天数：week=一周，month=一个月，quarter=三个月',
ADD COLUMN cycle_frequency ENUM('daily', 'every_two_days') DEFAULT NULL COMMENT '循环频率：daily=每天，every_two_days=两天一次',
ADD COLUMN start_date DATE DEFAULT NULL COMMENT '开始日期',
ADD COLUMN start_time TIME DEFAULT NULL COMMENT '开始时间';

-- 为新字段添加索引以提高查询性能
CREATE INDEX idx_plans_type_start ON plans(plan_type, start_date);
CREATE INDEX idx_plans_cycle ON plans(cycle_duration, cycle_frequency);

-- 查看修改后的表结构
-- DESCRIBE plans;
