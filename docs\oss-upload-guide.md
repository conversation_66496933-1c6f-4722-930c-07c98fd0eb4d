# 阿里云 OSS 图片上传功能使用指南

## 功能概述

本项目集成了阿里云对象存储服务（OSS），提供了完整的图片上传、批量上传和文件删除功能。支持管理端和小程序端的文件上传需求。

## 配置说明

### 1. 阿里云 OSS 配置

在 `src/config.js` 文件中配置阿里云 OSS 相关参数：

```javascript
// 阿里云OSS配置
export const AliOSS = {
  region: 'oss-cn-hangzhou', // OSS区域，如：oss-cn-hangzhou
  accessKeyId: 'your-access-key-id', // 阿里云AccessKey ID
  accessKeySecret: 'your-access-key-secret', // 阿里云AccessKey Secret
  bucket: 'your-bucket-name', // OSS存储桶名称
  endpoint: 'https://oss-cn-hangzhou.aliyuncs.com', // OSS访问域名
  customDomain: '', // 自定义域名（可选）
  folder: 'meditation-app/' // 上传文件夹前缀
}
```

### 2. 配置参数说明

- **region**: OSS 服务区域，需要与你的存储桶区域一致
- **accessKeyId**: 阿里云访问密钥 ID
- **accessKeySecret**: 阿里云访问密钥 Secret
- **bucket**: OSS 存储桶名称
- **endpoint**: OSS 访问端点，根据区域设置
- **customDomain**: 自定义域名（可选），如果配置了自定义域名，返回的 URL 将使用自定义域名
- **folder**: 文件上传的默认文件夹前缀

### 3. 获取阿里云访问密钥

1. 登录阿里云控制台
2. 进入 AccessKey 管理页面
3. 创建新的 AccessKey 或使用现有的
4. 记录 AccessKey ID 和 AccessKey Secret

### 4. 创建 OSS 存储桶

1. 登录阿里云 OSS 控制台
2. 创建新的存储桶（Bucket）
3. 设置存储桶权限为"公共读"（如果需要直接访问图片）
4. 配置跨域规则（CORS）以支持前端上传

## API 接口说明

### 管理端接口

#### 1. 单图上传
- **接口**: `POST /api/admin/oss/upload`
- **权限**: 需要管理员认证
- **参数**: 
  - `file`: 图片文件（必需）
  - `folder`: 上传文件夹（可选）

#### 2. 批量上传
- **接口**: `POST /api/admin/oss/upload/multiple`
- **权限**: 需要管理员认证
- **参数**: 
  - `files`: 图片文件数组（必需）
  - `folder`: 上传文件夹（可选）

#### 3. 删除文件
- **接口**: `DELETE /api/admin/oss/delete`
- **权限**: 需要管理员认证
- **参数**: 
  - `fileName`: 要删除的文件名（必需）

### 小程序端接口

#### 1. 单图上传
- **接口**: `POST /api/oss/upload`
- **权限**: 需要用户认证
- **参数**: 
  - `file`: 图片文件（必需）
  - `folder`: 上传文件夹（可选）

#### 2. 批量上传
- **接口**: `POST /api/oss/upload/multiple`
- **权限**: 需要用户认证
- **参数**: 
  - `files`: 图片文件数组（必需）
  - `folder`: 上传文件夹（可选）

## 文件限制

- **支持格式**: jpg, jpeg, png, gif, webp
- **文件大小**: 单个文件最大 5MB
- **文件命名**: 自动生成唯一文件名（时间戳 + UUID）

## 响应格式

### 成功响应
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "url": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/meditation-app/avatars/1640995200000_abc12345.jpg",
    "name": "meditation-app/avatars/1640995200000_abc12345.jpg",
    "size": 102400
  }
}
```

### 批量上传响应
```json
{
  "code": 200,
  "message": "批量上传完成",
  "data": {
    "success": [
      {
        "url": "https://your-bucket.oss-cn-hangzhou.aliyuncs.com/meditation-app/gallery/1640995200000_abc12345.jpg",
        "name": "meditation-app/gallery/1640995200000_abc12345.jpg",
        "size": 102400
      }
    ],
    "failed": [
      {
        "name": "invalid-file.txt",
        "error": "不支持的文件类型"
      }
    ]
  }
}
```

### 错误响应
```json
{
  "code": 400,
  "message": "未选择文件"
}
```

## 使用示例

### JavaScript/前端调用示例

```javascript
// 单图上传
const uploadImage = async (file, folder = '') => {
  const formData = new FormData()
  formData.append('file', file)
  if (folder) {
    formData.append('folder', folder)
  }

  const response = await fetch('/api/oss/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  })

  return await response.json()
}

// 批量上传
const uploadMultipleImages = async (files, folder = '') => {
  const formData = new FormData()
  files.forEach(file => {
    formData.append('files', file)
  })
  if (folder) {
    formData.append('folder', folder)
  }

  const response = await fetch('/api/oss/upload/multiple', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  })

  return await response.json()
}
```

### 微信小程序调用示例

```javascript
// 小程序上传图片
wx.chooseImage({
  count: 1,
  success: (res) => {
    const tempFilePath = res.tempFilePaths[0]
    
    wx.uploadFile({
      url: 'https://your-domain.com/api/oss/upload',
      filePath: tempFilePath,
      name: 'file',
      formData: {
        'folder': 'user-avatars/'
      },
      header: {
        'Authorization': `Bearer ${token}`
      },
      success: (uploadRes) => {
        const data = JSON.parse(uploadRes.data)
        console.log('上传成功:', data)
      }
    })
  }
})
```

## 安全建议

1. **访问密钥安全**: 不要将 AccessKey 信息提交到代码仓库
2. **权限控制**: 建议使用 RAM 子账号，并设置最小权限原则
3. **存储桶权限**: 根据实际需求设置存储桶的访问权限
4. **防盗链**: 配置 Referer 防盗链规则
5. **HTTPS**: 建议使用 HTTPS 协议访问 OSS

## 故障排除

### 常见错误

1. **AccessDenied**: 检查 AccessKey 权限和存储桶权限
2. **NoSuchBucket**: 检查存储桶名称和区域配置
3. **InvalidAccessKeyId**: 检查 AccessKey ID 是否正确
4. **SignatureDoesNotMatch**: 检查 AccessKey Secret 是否正确
5. **RequestTimeout**: 检查网络连接和 OSS 服务状态

### 调试建议

1. 检查控制台日志输出
2. 验证 OSS 配置参数
3. 测试网络连接
4. 查看阿里云 OSS 控制台的访问日志

## 更新日志

- v1.0.0: 初始版本，支持单图上传、批量上传和文件删除功能
