import Sequelize from 'sequelize'
import sequelize from '../lib/sequelize.js'

const Plan = sequelize.define('plans', {
  id: {
    type: Sequelize.BIGINT,
    primaryKey: true,
    autoIncrement: true
  },
  user_id: {
    type: Sequelize.BIGINT,
    allowNull: false
  },
  plan_date: {
    type: Sequelize.DATEONLY,
    allowNull: false,
    comment: '计划日期'
  },
  created_at: {
    type: Sequelize.DATE,
    defaultValue: Sequelize.NOW
  },
  plan_type: {
    type: Sequelize.ENUM('once', 'recurring'),
    defaultValue: 'once',
    comment: '计划类型：once=一次性，recurring=循环'
  },
  cycle_duration: {
    type: Sequelize.ENUM('week', 'month', 'quarter'),
    allowNull: true,
    comment: '循环天数：week=一周，month=一个月，quarter=三个月'
  },
  cycle_frequency: {
    type: Sequelize.ENUM('daily', 'every_two_days'),
    allowNull: true,
    comment: '循环频率：daily=每天，every_two_days=两天一次'
  },
  start_date: {
    type: Sequelize.DATEONLY,
    allowNull: true,
    comment: '开始日期'
  },
  start_time: {
    type: Sequelize.TIME,
    allowNull: true,
    comment: '开始时间'
  }
}, {
  timestamps: false,
  tableName: 'plans'
})

export default Plan