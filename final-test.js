/**
 * 最终测试脚本 - 验证所有统计数据修复
 */

import moment from 'moment'
import { generateAllStats } from './src/scripts/aggregateStats.js'
import { UserMeditationStats } from './src/models/associations.js'

async function finalTest() {
  console.log('=== 最终测试：验证统计数据修复 ===')
  
  try {
    // 1. 检查当前数据库中的统计数据
    console.log('\n1. 检查数据库中现有的统计数据...')
    
    const dayStats = await UserMeditationStats.count({ where: { period_type: 'day' } })
    const weekStats = await UserMeditationStats.count({ where: { period_type: 'week' } })
    const monthStats = await UserMeditationStats.count({ where: { period_type: 'month' } })
    const yearStats = await UserMeditationStats.count({ where: { period_type: 'year' } })
    
    console.log(`现有数据统计:`)
    console.log(`  天统计: ${dayStats} 条`)
    console.log(`  周统计: ${weekStats} 条`)
    console.log(`  月统计: ${monthStats} 条`)
    console.log(`  年统计: ${yearStats} 条`)
    
    // 2. 生成所有类型的统计数据
    console.log('\n2. 生成所有类型的统计数据...')
    const result = await generateAllStats()
    
    console.log('生成结果:')
    console.log(`  周统计: ${result.weekly.length} 条`)
    console.log(`  月统计: ${result.monthly.length} 条`)
    console.log(`  年统计: ${result.yearly.length} 条`)
    
    // 3. 再次检查数据库中的统计数据
    console.log('\n3. 检查生成后的统计数据...')
    
    const newDayStats = await UserMeditationStats.count({ where: { period_type: 'day' } })
    const newWeekStats = await UserMeditationStats.count({ where: { period_type: 'week' } })
    const newMonthStats = await UserMeditationStats.count({ where: { period_type: 'month' } })
    const newYearStats = await UserMeditationStats.count({ where: { period_type: 'year' } })
    
    console.log(`生成后数据统计:`)
    console.log(`  天统计: ${newDayStats} 条`)
    console.log(`  周统计: ${newWeekStats} 条 (新增 ${newWeekStats - weekStats} 条)`)
    console.log(`  月统计: ${newMonthStats} 条 (新增 ${newMonthStats - monthStats} 条)`)
    console.log(`  年统计: ${newYearStats} 条 (新增 ${newYearStats - yearStats} 条)`)
    
    // 4. 显示一些示例数据
    console.log('\n4. 示例数据:')
    
    if (result.weekly.length > 0) {
      console.log('周统计示例:')
      result.weekly.slice(0, 3).forEach(stat => {
        const weekStart = moment(stat.period_date).format('YYYY-MM-DD')
        const weekEnd = moment(stat.period_date).add(6, 'days').format('YYYY-MM-DD')
        console.log(`  用户${stat.user_id}: ${weekStart}~${weekEnd}, 任务${stat.tasks_completed}个, 时长${stat.meditation_duration}秒`)
      })
    }
    
    if (result.monthly.length > 0) {
      console.log('月统计示例:')
      result.monthly.slice(0, 2).forEach(stat => {
        const monthName = moment(stat.period_date).format('YYYY年MM月')
        console.log(`  用户${stat.user_id}: ${monthName}, 任务${stat.tasks_completed}个, 时长${stat.meditation_duration}秒`)
      })
    }
    
    if (result.yearly.length > 0) {
      console.log('年统计示例:')
      result.yearly.slice(0, 1).forEach(stat => {
        const yearName = moment(stat.period_date).format('YYYY年')
        console.log(`  用户${stat.user_id}: ${yearName}, 任务${stat.tasks_completed}个, 时长${stat.meditation_duration}秒`)
      })
    }
    
    // 5. 验证周统计的日期范围
    console.log('\n5. 验证周统计的日期范围...')
    if (result.weekly.length > 0) {
      const sampleWeekStat = result.weekly[0]
      const weekStart = moment(sampleWeekStat.period_date)
      const dayOfWeek = weekStart.day() // 0=周日, 1=周一
      console.log(`周统计开始日期: ${weekStart.format('YYYY-MM-DD dddd')}`)
      console.log(`是否为周一开始: ${dayOfWeek === 1 ? '✅ 是' : '❌ 否 (当前是' + weekStart.format('dddd') + ')'}`)
    }
    
    // 6. 总结
    console.log('\n=== 测试总结 ===')
    console.log(`✅ 天统计数据: ${newDayStats > 0 ? '有数据' : '无数据'}`)
    console.log(`${newWeekStats > 0 ? '✅' : '❌'} 周统计数据: ${newWeekStats > 0 ? '生成成功' : '生成失败'}`)
    console.log(`${newMonthStats > 0 ? '✅' : '❌'} 月统计数据: ${newMonthStats > 0 ? '生成成功' : '生成失败'}`)
    console.log(`${newYearStats > 0 ? '✅' : '❌'} 年统计数据: ${newYearStats > 0 ? '生成成功' : '生成失败'}`)
    
    if (newWeekStats > 0 && newMonthStats > 0 && newYearStats > 0) {
      console.log('\n🎉 所有统计数据修复成功！现在可以正常查询周、月、年统计数据了。')
    } else {
      console.log('\n⚠️  部分统计数据生成失败，请检查天统计数据是否存在。')
    }
    
  } catch (error) {
    console.error('测试过程中出错:', error)
  }
  
  process.exit(0)
}

finalTest()
