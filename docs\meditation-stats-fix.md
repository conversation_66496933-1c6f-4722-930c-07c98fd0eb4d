# 用户冥想统计接口问题修复

## 问题描述

用户冥想统计接口测试时出现以下问题：
1. 接口调用报错：`"code": 400, "message": "请提供有效的周期类型 (day/week/month/year)"`
2. 只有按天统计有数据，按周、月、年统计都没有数据

## 问题分析

### 1. 接口调用错误
- **原因**：测试文件中的请求缺少必需的 `period_type` 参数
- **影响**：所有冥想统计相关的测试请求都会失败

### 2. 周月年统计数据缺失
- **原因**：系统中缺少从天数据聚合生成周、月、年统计数据的逻辑
- **影响**：用户无法查看周、月、年的统计数据

### 3. 周统计数据生成问题
- **原因**：moment.js 默认使用周日作为一周的开始，与常见的周一开始不符
- **影响**：周统计数据的时间范围不正确，可能导致数据缺失

## 解决方案

### 1. 修复测试文件参数问题

已修复 `api-tests/user/profile.http` 文件中的所有测试请求，添加了必需的 `period_type` 参数：

```http
# 修复前（错误）
GET {{apiUrl}}/user/meditation-stats
Authorization: Bearer {{testUserToken}}

# 修复后（正确）
GET {{apiUrl}}/user/meditation-stats?period_type=day
Authorization: Bearer {{testUserToken}}
```

### 2. 创建统计数据聚合脚本

创建了 `src/scripts/aggregateStats.js` 脚本，包含以下功能：

- **聚合用户统计数据**：从天数据生成周、月、年统计
- **批量处理**：支持为所有用户或指定用户生成统计
- **灵活配置**：支持指定日期范围和统计类型

### 3. 修复周统计计算问题

- **使用ISO周**：将 `startOf('week')` 改为 `startOf('isoWeek')`，确保周一作为一周的开始
- **历史数据支持**：添加 `generateHistoricalWeeklyStats` 函数，支持生成过去几周的统计数据
- **智能聚合**：`generateAllStats` 函数默认会生成历史周数据，确保数据完整性

#### 主要函数：

- `generateWeeklyStats(userId, targetDate)` - 生成周统计（使用ISO周，周一开始）
- `generateHistoricalWeeklyStats(userId, weeksBack)` - 生成历史周统计数据
- `generateMonthlyStats(userId, targetDate)` - 生成月统计
- `generateYearlyStats(userId, targetDate)` - 生成年统计
- `generateAllStats(userId, targetDate, includeHistorical)` - 生成所有类型统计

### 3. 添加管理员接口

在 `AdminUserController` 中添加了 `generateMeditationStats` 方法，支持通过API触发统计数据生成：

```javascript
POST /admin/users/generate-meditation-stats
{
  "user_id": 1,           // 可选，指定用户ID
  "stats_type": "all",    // week/month/year/week_historical/all
  "target_date": "2024-01-15",  // 可选，指定日期
  "weeks_back": 4         // 可选，历史周统计回溯周数
}
```

## 使用方法

### 1. 命令行执行脚本

```bash
# 为所有用户生成所有类型统计
node src/scripts/aggregateStats.js

# 为指定用户生成周统计
node src/scripts/aggregateStats.js --user 1 --type week

# 为指定日期生成月统计
node src/scripts/aggregateStats.js --date 2024-01-15 --type month
```

### 2. 通过管理员API调用

使用 `api-tests/admin/meditation-stats-aggregation.http` 中的测试请求：

```http
# 生成所有用户的所有统计数据
POST {{baseUrl}}/admin/users/generate-meditation-stats
Authorization: Bearer {{adminToken}}
Content-Type: application/json

{
  "stats_type": "all"
}
```

### 3. 验证修复结果

使用修复后的测试文件验证：

```http
# 查看周统计数据
GET {{apiUrl}}/user/meditation-stats?period_type=week
Authorization: Bearer {{testUserToken}}

# 查看月统计数据
GET {{apiUrl}}/user/meditation-stats?period_type=month
Authorization: Bearer {{testUserToken}}
```

## 建议的定时任务

为了保持统计数据的实时性，建议设置定时任务：

```bash
# 每天凌晨1点生成前一天的统计数据
0 1 * * * cd /path/to/project && node src/scripts/aggregateStats.js

# 每周一凌晨2点生成上周的统计数据
0 2 * * 1 cd /path/to/project && node src/scripts/aggregateStats.js --type week

# 每月1号凌晨3点生成上月的统计数据
0 3 1 * * cd /path/to/project && node src/scripts/aggregateStats.js --type month
```

## 文件变更清单

### 新增文件：
- `src/scripts/aggregateStats.js` - 统计数据聚合脚本
- `api-tests/admin/meditation-stats-aggregation.http` - 聚合功能测试文件
- `test-aggregation.js` - 简单测试脚本
- `debug-week-stats.js` - 周统计调试脚本
- `test-week-stats.js` - 周统计测试脚本
- `docs/meditation-stats-fix.md` - 本文档

### 修改文件：
- `api-tests/user/profile.http` - 修复测试请求参数
- `src/controllers/adminUser.js` - 添加聚合接口
- `src/routes/admin-routes.js` - 添加聚合路由

## 注意事项

1. **数据依赖**：周、月、年统计数据依赖于天统计数据，确保天数据完整
2. **性能考虑**：大量数据聚合时可能耗时较长，建议在低峰期执行
3. **数据一致性**：聚合过程中如果有新的天数据产生，可能需要重新聚合
4. **错误处理**：脚本包含完善的错误处理，单个用户失败不会影响整体执行

## 测试验证

执行以下步骤验证修复效果：

1. 启动服务器：`npm run dev`
2. 运行聚合脚本：`node test-aggregation.js`
3. 使用修复后的测试文件验证接口
4. 检查数据库中是否生成了周、月、年统计数据

修复完成后，用户应该能够正常查看所有周期类型的冥想统计数据。
