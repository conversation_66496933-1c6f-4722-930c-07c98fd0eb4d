/**
 * 调试周统计数据生成问题
 */

import moment from 'moment'
import { UserMeditationStats } from './src/models/associations.js'
import sequelize from 'sequelize'

const { Op } = sequelize

async function debugWeekStats() {
  console.log('=== 调试周统计数据生成 ===')
  
  // 1. 检查当前日期和周计算
  const now = moment()
  console.log('当前日期:', now.format('YYYY-MM-DD dddd'))
  
  // 默认周计算（周日开始）
  const weekStartDefault = now.clone().startOf('week').format('YYYY-MM-DD')
  const weekEndDefault = now.clone().endOf('week').format('YYYY-MM-DD')
  console.log('默认周范围（周日开始）:', weekStartDefault, '到', weekEndDefault)
  
  // 周一开始的周计算
  const weekStartMonday = now.clone().startOf('isoWeek').format('YYYY-MM-DD')
  const weekEndMonday = now.clone().endOf('isoWeek').format('YYYY-MM-DD')
  console.log('ISO周范围（周一开始）:', weekStartMonday, '到', weekEndMonday)
  
  // 2. 检查数据库中的天统计数据
  console.log('\n=== 检查天统计数据 ===')
  
  try {
    const dayStats = await UserMeditationStats.findAll({
      where: {
        period_type: 'day'
      },
      order: [['period_date', 'DESC']],
      limit: 10
    })
    
    console.log('最近10条天统计数据:')
    dayStats.forEach(stat => {
      const date = moment(stat.period_date)
      console.log(`- ${stat.period_date} (${date.format('dddd')}) 用户${stat.user_id}: 任务${stat.tasks_completed}个, 时长${stat.meditation_duration}秒`)
    })
    
    // 3. 检查当前周范围内的数据（默认周日开始）
    console.log('\n=== 检查当前周数据（周日开始）===')
    const currentWeekStats = await UserMeditationStats.findAll({
      where: {
        period_type: 'day',
        period_date: {
          [Op.between]: [weekStartDefault, weekEndDefault]
        }
      },
      order: [['period_date', 'ASC']]
    })
    
    console.log(`当前周（${weekStartDefault} 到 ${weekEndDefault}）的天统计数据:`)
    if (currentWeekStats.length === 0) {
      console.log('- 没有数据')
    } else {
      currentWeekStats.forEach(stat => {
        console.log(`- ${stat.period_date} 用户${stat.user_id}: 任务${stat.tasks_completed}个`)
      })
    }
    
    // 4. 检查当前周范围内的数据（周一开始）
    console.log('\n=== 检查当前周数据（周一开始）===')
    const currentWeekStatsISO = await UserMeditationStats.findAll({
      where: {
        period_type: 'day',
        period_date: {
          [Op.between]: [weekStartMonday, weekEndMonday]
        }
      },
      order: [['period_date', 'ASC']]
    })
    
    console.log(`当前周（${weekStartMonday} 到 ${weekEndMonday}）的天统计数据:`)
    if (currentWeekStatsISO.length === 0) {
      console.log('- 没有数据')
    } else {
      currentWeekStatsISO.forEach(stat => {
        console.log(`- ${stat.period_date} 用户${stat.user_id}: 任务${stat.tasks_completed}个`)
      })
    }
    
    // 5. 检查过去几周的数据
    console.log('\n=== 检查过去几周的数据 ===')
    for (let i = 0; i < 4; i++) {
      const pastWeek = now.clone().subtract(i, 'weeks')
      const pastWeekStart = pastWeek.clone().startOf('isoWeek').format('YYYY-MM-DD')
      const pastWeekEnd = pastWeek.clone().endOf('isoWeek').format('YYYY-MM-DD')
      
      const pastWeekStats = await UserMeditationStats.findAll({
        where: {
          period_type: 'day',
          period_date: {
            [Op.between]: [pastWeekStart, pastWeekEnd]
          }
        }
      })
      
      console.log(`第${i}周前（${pastWeekStart} 到 ${pastWeekEnd}）: ${pastWeekStats.length}条数据`)
    }
    
    // 6. 检查已存在的周统计数据
    console.log('\n=== 检查已存在的周统计数据 ===')
    const existingWeekStats = await UserMeditationStats.findAll({
      where: {
        period_type: 'week'
      },
      order: [['period_date', 'DESC']],
      limit: 5
    })
    
    console.log('已存在的周统计数据:')
    if (existingWeekStats.length === 0) {
      console.log('- 没有周统计数据')
    } else {
      existingWeekStats.forEach(stat => {
        console.log(`- ${stat.period_date} 用户${stat.user_id}: 任务${stat.tasks_completed}个, 时长${stat.meditation_duration}秒`)
      })
    }
    
  } catch (error) {
    console.error('调试过程中出错:', error)
  }
  
  process.exit(0)
}

debugWeekStats()
