import request from 'supertest'
import app from '../src/app.js'
import fs from 'fs'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

describe('OSS Upload API Tests', () => {
  let adminToken = ''
  let userToken = ''

  // 在测试开始前获取认证令牌
  beforeAll(async () => {
    // 这里需要根据实际的登录接口获取令牌
    // 示例代码，需要根据实际情况调整
    
    // 管理员登录
    const adminLoginResponse = await request(app)
      .post('/api/admin/login')
      .send({
        username: 'admin',
        password: 'admin123'
      })
    
    if (adminLoginResponse.body.code === 200) {
      adminToken = adminLoginResponse.body.data.token
    }

    // 用户登录（如果有用户登录接口）
    // const userLoginResponse = await request(app)
    //   .post('/api/user/login')
    //   .send({
    //     openid: 'test_openid'
    //   })
    
    // if (userLoginResponse.body.code === 200) {
    //   userToken = userLoginResponse.body.data.token
    // }
  })

  describe('Admin OSS Upload', () => {
    test('应该成功上传单个图片', async () => {
      // 创建测试图片文件
      const testImagePath = path.join(__dirname, 'test-image.jpg')
      
      // 如果测试图片不存在，跳过测试
      if (!fs.existsSync(testImagePath)) {
        console.log('测试图片不存在，跳过测试')
        return
      }

      const response = await request(app)
        .post('/api/admin/oss/upload')
        .set('Authorization', `Bearer ${adminToken}`)
        .attach('file', testImagePath)
        .field('folder', 'test/')

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(200)
      expect(response.body.message).toBe('上传成功')
      expect(response.body.data).toHaveProperty('url')
      expect(response.body.data).toHaveProperty('name')
      expect(response.body.data).toHaveProperty('size')
    }, 30000) // 30秒超时

    test('应该拒绝不支持的文件类型', async () => {
      // 创建测试文本文件
      const testFilePath = path.join(__dirname, 'test-file.txt')
      fs.writeFileSync(testFilePath, 'This is a test file')

      const response = await request(app)
        .post('/api/admin/oss/upload')
        .set('Authorization', `Bearer ${adminToken}`)
        .attach('file', testFilePath)

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(400)
      expect(response.body.message).toContain('不支持的文件类型')

      // 清理测试文件
      fs.unlinkSync(testFilePath)
    })

    test('应该拒绝未认证的请求', async () => {
      const response = await request(app)
        .post('/api/admin/oss/upload')
        .attach('file', Buffer.from('fake image'), 'test.jpg')

      expect(response.status).toBe(401)
    })

    test('应该拒绝没有文件的请求', async () => {
      const response = await request(app)
        .post('/api/admin/oss/upload')
        .set('Authorization', `Bearer ${adminToken}`)

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(400)
      expect(response.body.message).toBe('未选择文件')
    })
  })

  describe('User OSS Upload', () => {
    test('应该成功上传单个图片（如果有用户令牌）', async () => {
      if (!userToken) {
        console.log('没有用户令牌，跳过用户上传测试')
        return
      }

      const testImagePath = path.join(__dirname, 'test-image.jpg')
      
      if (!fs.existsSync(testImagePath)) {
        console.log('测试图片不存在，跳过测试')
        return
      }

      const response = await request(app)
        .post('/api/oss/upload')
        .set('Authorization', `Bearer ${userToken}`)
        .attach('file', testImagePath)
        .field('folder', 'user-test/')

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(200)
      expect(response.body.message).toBe('上传成功')
    }, 30000)
  })

  describe('OSS File Delete', () => {
    test('应该成功删除文件', async () => {
      const response = await request(app)
        .delete('/api/admin/oss/delete')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({
          fileName: 'test/non-existent-file.jpg'
        })

      // 即使文件不存在，OSS 删除操作通常也会返回成功
      expect(response.status).toBe(200)
    })

    test('应该拒绝空文件名', async () => {
      const response = await request(app)
        .delete('/api/admin/oss/delete')
        .set('Authorization', `Bearer ${adminToken}`)
        .send({})

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(400)
      expect(response.body.message).toBe('文件名不能为空')
    })
  })

  describe('OSS Multiple Upload', () => {
    test('应该成功批量上传图片', async () => {
      const testImagePath1 = path.join(__dirname, 'test-image1.jpg')
      const testImagePath2 = path.join(__dirname, 'test-image2.jpg')
      
      // 如果测试图片不存在，跳过测试
      if (!fs.existsSync(testImagePath1) || !fs.existsSync(testImagePath2)) {
        console.log('测试图片不存在，跳过批量上传测试')
        return
      }

      const response = await request(app)
        .post('/api/admin/oss/upload/multiple')
        .set('Authorization', `Bearer ${adminToken}`)
        .attach('files', testImagePath1)
        .attach('files', testImagePath2)
        .field('folder', 'batch-test/')

      expect(response.status).toBe(200)
      expect(response.body.code).toBe(200)
      expect(response.body.message).toBe('批量上传完成')
      expect(response.body.data).toHaveProperty('success')
      expect(response.body.data).toHaveProperty('failed')
    }, 60000) // 60秒超时
  })
})

// 测试工具函数
describe('OSS Upload Utils', () => {
  test('应该正确验证文件类型', () => {
    const OSSUploadController = require('../src/controllers/ossUpload.js').default
    
    expect(OSSUploadController.validateFileType('image/jpeg')).toBe(true)
    expect(OSSUploadController.validateFileType('image/png')).toBe(true)
    expect(OSSUploadController.validateFileType('image/gif')).toBe(true)
    expect(OSSUploadController.validateFileType('text/plain')).toBe(false)
    expect(OSSUploadController.validateFileType('application/pdf')).toBe(false)
  })

  test('应该正确验证文件大小', () => {
    const OSSUploadController = require('../src/controllers/ossUpload.js').default
    
    expect(OSSUploadController.validateFileSize(1024 * 1024)).toBe(true) // 1MB
    expect(OSSUploadController.validateFileSize(5 * 1024 * 1024)).toBe(true) // 5MB
    expect(OSSUploadController.validateFileSize(6 * 1024 * 1024)).toBe(false) // 6MB
  })

  test('应该生成唯一的文件名', () => {
    const OSSUploadController = require('../src/controllers/ossUpload.js').default
    
    const fileName1 = OSSUploadController.generateFileName('test.jpg', 'folder/')
    const fileName2 = OSSUploadController.generateFileName('test.jpg', 'folder/')
    
    expect(fileName1).not.toBe(fileName2)
    expect(fileName1).toMatch(/^folder\/\d+_[a-f0-9]{8}\.jpg$/)
    expect(fileName2).toMatch(/^folder\/\d+_[a-f0-9]{8}\.jpg$/)
  })
})
