<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSS 图片上传示例</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .file-input {
            margin: 10px 0;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .upload-btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .preview {
            max-width: 200px;
            max-height: 200px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>阿里云 OSS 图片上传示例</h1>
    
    <!-- 单图上传 -->
    <div class="upload-section">
        <h2>单图上传</h2>
        <div class="file-input">
            <label for="singleFile">选择图片文件：</label>
            <input type="file" id="singleFile" accept="image/*">
        </div>
        <div class="file-input">
            <label for="singleFolder">上传文件夹（可选）：</label>
            <input type="text" id="singleFolder" placeholder="例如：avatars/" value="test/">
        </div>
        <button class="upload-btn" onclick="uploadSingle()">上传单图</button>
        <div id="singleResult"></div>
        <img id="singlePreview" class="preview" style="display: none;">
    </div>

    <!-- 批量上传 -->
    <div class="upload-section">
        <h2>批量上传</h2>
        <div class="file-input">
            <label for="multipleFiles">选择多个图片文件：</label>
            <input type="file" id="multipleFiles" accept="image/*" multiple>
        </div>
        <div class="file-input">
            <label for="multipleFolder">上传文件夹（可选）：</label>
            <input type="text" id="multipleFolder" placeholder="例如：gallery/" value="batch-test/">
        </div>
        <button class="upload-btn" onclick="uploadMultiple()">批量上传</button>
        <div id="multipleResult"></div>
        <div id="multiplePreviews"></div>
    </div>

    <!-- 删除文件 -->
    <div class="upload-section">
        <h2>删除文件</h2>
        <div class="file-input">
            <label for="deleteFileName">文件名（OSS中的完整路径）：</label>
            <input type="text" id="deleteFileName" placeholder="例如：duorou-meditation-web/test/1640995200000_abc12345.jpg">
        </div>
        <button class="upload-btn" onclick="deleteFile()">删除文件</button>
        <div id="deleteResult"></div>
    </div>

    <script>
        // 配置信息
        const API_BASE_URL = 'http://localhost:3004';
        const TOKEN = 'your-jwt-token-here'; // 请替换为实际的JWT令牌

        // 显示结果
        function showResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${isSuccess ? 'success' : 'error'}">${message}</div>`;
        }

        // 单图上传
        async function uploadSingle() {
            const fileInput = document.getElementById('singleFile');
            const folderInput = document.getElementById('singleFolder');
            const resultDiv = document.getElementById('singleResult');
            const previewImg = document.getElementById('singlePreview');

            if (!fileInput.files[0]) {
                showResult('singleResult', '请选择文件', false);
                return;
            }

            const formData = new FormData();
            formData.append('file', fileInput.files[0]);
            if (folderInput.value) {
                formData.append('folder', folderInput.value);
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/oss/upload`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showResult('singleResult', `上传成功！<br>URL: <a href="${result.data.url}" target="_blank">${result.data.url}</a><br>文件名: ${result.data.name}<br>大小: ${(result.data.size / 1024).toFixed(2)} KB`);
                    
                    // 显示预览
                    previewImg.src = result.data.url;
                    previewImg.style.display = 'block';
                } else {
                    showResult('singleResult', `上传失败: ${result.message}`, false);
                }
            } catch (error) {
                showResult('singleResult', `上传失败: ${error.message}`, false);
            }
        }

        // 批量上传
        async function uploadMultiple() {
            const fileInput = document.getElementById('multipleFiles');
            const folderInput = document.getElementById('multipleFolder');
            const resultDiv = document.getElementById('multipleResult');
            const previewsDiv = document.getElementById('multiplePreviews');

            if (!fileInput.files.length) {
                showResult('multipleResult', '请选择文件', false);
                return;
            }

            const formData = new FormData();
            for (let i = 0; i < fileInput.files.length; i++) {
                formData.append('files', fileInput.files[i]);
            }
            if (folderInput.value) {
                formData.append('folder', folderInput.value);
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/oss/upload/multiple`, {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`
                    },
                    body: formData
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    let message = `批量上传完成！<br>成功: ${result.data.success.length} 个文件<br>失败: ${result.data.failed.length} 个文件<br><br>`;
                    
                    // 显示成功的文件
                    if (result.data.success.length > 0) {
                        message += '<strong>成功上传的文件:</strong><br>';
                        result.data.success.forEach(file => {
                            message += `• <a href="${file.url}" target="_blank">${file.name}</a> (${(file.size / 1024).toFixed(2)} KB)<br>`;
                        });
                    }
                    
                    // 显示失败的文件
                    if (result.data.failed.length > 0) {
                        message += '<br><strong>上传失败的文件:</strong><br>';
                        result.data.failed.forEach(file => {
                            message += `• ${file.name}: ${file.error}<br>`;
                        });
                    }
                    
                    showResult('multipleResult', message);
                    
                    // 显示预览
                    previewsDiv.innerHTML = '';
                    result.data.success.forEach(file => {
                        const img = document.createElement('img');
                        img.src = file.url;
                        img.className = 'preview';
                        img.style.margin = '5px';
                        previewsDiv.appendChild(img);
                    });
                } else {
                    showResult('multipleResult', `批量上传失败: ${result.message}`, false);
                }
            } catch (error) {
                showResult('multipleResult', `批量上传失败: ${error.message}`, false);
            }
        }

        // 删除文件
        async function deleteFile() {
            const fileNameInput = document.getElementById('deleteFileName');

            if (!fileNameInput.value) {
                showResult('deleteResult', '请输入文件名', false);
                return;
            }

            try {
                const response = await fetch(`${API_BASE_URL}/api/oss/delete`, {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${TOKEN}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        fileName: fileNameInput.value
                    })
                });

                const result = await response.json();
                
                if (result.code === 200) {
                    showResult('deleteResult', '文件删除成功！');
                    fileNameInput.value = '';
                } else {
                    showResult('deleteResult', `删除失败: ${result.message}`, false);
                }
            } catch (error) {
                showResult('deleteResult', `删除失败: ${error.message}`, false);
            }
        }

        // 页面加载完成后的提示
        window.onload = function() {
            if (TOKEN === 'your-jwt-token-here') {
                document.body.insertAdjacentHTML('afterbegin', 
                    '<div class="result error" style="margin-bottom: 20px;">' +
                    '⚠️ 请先在代码中设置正确的JWT令牌（TOKEN变量）才能正常使用上传功能！' +
                    '</div>'
                );
            }
        };
    </script>
</body>
</html>
