### 用户资料接口测试
### 引用全局变量
< ../common/variables.http

### 前置条件：需要先获取用户token
< ../auth/user-auth.http

@apiUrl = http://localhost:3004/api
@testUserToken = eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjEiLCJvcGVuaWQiOiJvcGVuaWRfdGVzdF8xMjMiLCJpYXQiOjE3NTQ5MTUzMzIsImV4cCI6MTc1NzUwNzMzMn0.kkI_Fiw8KcrJETT8JTw5eDBLtUIj25EoAOeELtBwEj0

### 1. 获取用户基础信息 - 正常获取
GET {{apiUrl}}/user/profile
Authorization: Bearer {{testUserToken}}


### 2. 获取用户基础信息 - 包含详细信息
GET {{apiUrl}}/user/profile?include_details=true
Authorization: Bearer {{testUserToken}}

### 3. 获取用户基础信息 - 包含统计数据
GET {{apiUrl}}/user/profile?include_stats=true
Authorization: Bearer {{testUserToken}}

### 4. 获取用户基础信息 - 包含等级信息
GET {{apiUrl}}/user/profile?include_level=true
Authorization: Bearer {{testUserToken}}

### 5. 获取用户基础信息 - 包含成就信息
GET {{apiUrl}}/user/profile?include_achievements=true
Authorization: Bearer {{testUserToken}}

### 6. 获取用户基础信息 - 完整信息
GET {{apiUrl}}/user/profile?include_details=true&include_stats=true&include_level=true&include_achievements=true
Authorization: Bearer {{testUserToken}}

### 7. 获取用户收藏列表 - 默认分页
GET {{apiUrl}}/user/favorites
Authorization: Bearer {{testUserToken}}

### 8. 获取用户收藏列表 - 指定分页
GET {{apiUrl}}/user/favorites?page={{defaultPage}}&limit={{defaultLimit}}
Authorization: Bearer {{testUserToken}}

### 9. 获取用户收藏列表 - 按类型筛选
GET {{apiUrl}}/user/favorites?type=meditation&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 10. 获取用户收藏列表 - 按时间排序
GET {{apiUrl}}/user/favorites?sort=created_at&order=desc&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 11. 获取用户多肉列表 - 默认获取
GET {{apiUrl}}/user/plants
Authorization: Bearer {{testUserToken}}

### 12. 获取用户多肉列表 - 包含详细信息
GET {{apiUrl}}/user/plants?include_details=true
Authorization: Bearer {{testUserToken}}

### 13. 获取用户多肉列表 - 按等级筛选
GET {{apiUrl}}/user/plants?level=2
Authorization: Bearer {{testUserToken}}

### 14. 获取用户多肉列表 - 按健康状态筛选
GET {{apiUrl}}/user/plants?health_status=healthy
Authorization: Bearer {{testUserToken}}

### 15. 获取用户多肉列表 - 按能量排序
GET {{apiUrl}}/user/plants?sort=energy&order=desc
Authorization: Bearer {{testUserToken}}

### 16. 获取用户冥想统计 - 按天统计
GET {{apiUrl}}/user/meditation-stats?period_type=day
Authorization: Bearer {{testUserToken}}

### 17. 获取用户冥想统计 - 按周统计
GET {{apiUrl}}/user/meditation-stats?period_type=week
Authorization: Bearer {{testUserToken}}

### 18. 获取用户冥想统计 - 按月统计
GET {{apiUrl}}/user/meditation-stats?period_type=month
Authorization: Bearer {{testUserToken}}

### 19. 获取用户冥想统计 - 按年统计
GET {{apiUrl}}/user/meditation-stats?period_type=year
Authorization: Bearer {{testUserToken}}

### 20. 获取用户冥想统计 - 按天统计带时间范围
GET {{apiUrl}}/user/meditation-stats?period_type=day&start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{testUserToken}}

### 21. 获取用户冥想统计 - 按月统计带时间范围
GET {{apiUrl}}/user/meditation-stats?period_type=month&start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{testUserToken}}

### 22. 权限测试 - 无token访问用户信息
GET {{apiUrl}}/user/profile

### 23. 权限测试 - 无效token访问用户信息
GET {{apiUrl}}/user/profile
Authorization: Bearer invalid_user_token

### 24. 权限测试 - 过期token访问用户信息
GET {{apiUrl}}/user/profile
Authorization: Bearer expired_user_token

### 25. 权限测试 - 无token访问收藏列表
GET {{apiUrl}}/user/favorites

### 26. 权限测试 - 无token访问多肉列表
GET {{apiUrl}}/user/plants

### 27. 权限测试 - 无token访问冥想统计
GET {{apiUrl}}/user/meditation-stats

### 28. 分页测试 - 收藏列表第一页
GET {{apiUrl}}/user/favorites?page=1&limit=1
Authorization: Bearer {{testUserToken}}

### 29. 分页测试 - 收藏列表超出范围页码
GET {{apiUrl}}/user/favorites?page=99999&limit=10
Authorization: Bearer {{testUserToken}}

### 30. 分页测试 - 收藏列表无效limit
GET {{apiUrl}}/user/favorites?page=1&limit=0
Authorization: Bearer {{testUserToken}}

### 31. 分页测试 - 收藏列表过大limit
GET {{apiUrl}}/user/favorites?page=1&limit=1000
Authorization: Bearer {{testUserToken}}

### 32. 参数测试 - 无效的筛选类型
GET {{apiUrl}}/user/favorites?type=invalid_type&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 33. 参数测试 - 无效的排序字段
GET {{apiUrl}}/user/favorites?sort=invalid_field&order=desc&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 34. 参数测试 - 无效的排序方向
GET {{apiUrl}}/user/favorites?sort=created_at&order=invalid_order&page=1&limit=10
Authorization: Bearer {{testUserToken}}

### 35. 参数测试 - 无效的周期类型
GET {{apiUrl}}/user/meditation-stats?period_type=invalid_period
Authorization: Bearer {{testUserToken}}

### 36. 参数测试 - 缺少周期类型
GET {{apiUrl}}/user/meditation-stats?start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer {{testUserToken}}

### 37. 参数测试 - 无效的时间格式
GET {{apiUrl}}/user/meditation-stats?period_type=day&start_date=invalid_date&end_date=2024-12-31
Authorization: Bearer {{testUserToken}}

### 38. 参数测试 - 开始时间晚于结束时间
GET {{apiUrl}}/user/meditation-stats?period_type=day&start_date=2024-12-31&end_date=2024-01-01
Authorization: Bearer {{testUserToken}}

### 38. 性能测试 - 大量收藏数据查询
GET {{apiUrl}}/user/favorites?page=1&limit=100
Authorization: Bearer {{testUserToken}}

### 39. 性能测试 - 复杂统计查询
GET {{apiUrl}}/user/meditation-stats?period_type=day&start_date=2023-01-01&end_date=2024-12-31
Authorization: Bearer {{testUserToken}}

### 40. 缓存测试 - 重复请求用户信息
GET {{apiUrl}}/user/profile
Authorization: Bearer {{testUserToken}}

###
GET {{apiUrl}}/user/profile
Authorization: Bearer {{testUserToken}}

### 41. 缓存测试 - 重复请求统计数据
GET {{apiUrl}}/user/meditation-stats?period_type=day
Authorization: Bearer {{testUserToken}}

###
GET {{apiUrl}}/user/meditation-stats?period_type=day
Authorization: Bearer {{testUserToken}}

### 42. 并发测试 - 同时请求多个接口
GET {{apiUrl}}/user/profile
Authorization: Bearer {{testUserToken}}

###
GET {{apiUrl}}/user/favorites
Authorization: Bearer {{testUserToken}}

###
GET {{apiUrl}}/user/plants
Authorization: Bearer {{testUserToken}}

###
GET {{apiUrl}}/user/meditation-stats?period_type=month
Authorization: Bearer {{testUserToken}}

### 43. 数据一致性测试 - 检查用户信息一致性
GET {{apiUrl}}/user/profile?include_stats=true
Authorization: Bearer {{testUserToken}}

### 44. 边界测试 - 新用户数据
# 注意：需要新用户token
GET {{apiUrl}}/user/profile
Authorization: Bearer new_user_token_here

### 45. 边界测试 - 高活跃用户数据
# 注意：需要高活跃用户token
GET {{apiUrl}}/user/meditation-stats?period_type=day&start_date=2024-01-01&end_date=2024-12-31
Authorization: Bearer active_user_token_here
