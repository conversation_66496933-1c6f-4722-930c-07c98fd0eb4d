### 阿里云OSS图片上传接口测试

### 1. 管理端 - OSS单图上传
POST http://localhost:3004/api/admin/oss/upload
Authorization: Bearer {{admin_token}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="test-image.jpg"
Content-Type: image/jpeg

< ./test-files/test-image.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="folder"

avatars/
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 2. 管理端 - OSS批量上传
POST http://localhost:3004/api/admin/oss/upload/multiple
Authorization: Bearer {{admin_token}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="test-image1.jpg"
Content-Type: image/jpeg

< ./test-files/test-image1.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="test-image2.jpg"
Content-Type: image/jpeg

< ./test-files/test-image2.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="folder"

gallery/
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 3. 管理端 - 删除OSS文件
DELETE http://localhost:3004/api/admin/oss/delete
Authorization: Bearer {{admin_token}}
Content-Type: application/json

{
  "fileName": "meditation-app/avatars/1640995200000_abc12345.jpg"
}

### 4. 小程序端 - OSS单图上传
POST http://localhost:3004/api/oss/upload
Authorization: Bearer {{user_token}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="file"; filename="user-avatar.jpg"
Content-Type: image/jpeg

< ./test-files/user-avatar.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="folder"

user-avatars/
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 5. 小程序端 - OSS批量上传
POST http://localhost:3004/api/oss/upload/multiple
Authorization: Bearer {{user_token}}
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW

------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="plant1.jpg"
Content-Type: image/jpeg

< ./test-files/plant1.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="files"; filename="plant2.jpg"
Content-Type: image/jpeg

< ./test-files/plant2.jpg
------WebKitFormBoundary7MA4YWxkTrZu0gW
Content-Disposition: form-data; name="folder"

plants/
------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 测试说明
# 1. 请先配置 src/config.js 中的阿里云OSS配置信息
# 2. 将 {{admin_token}} 和 {{user_token}} 替换为实际的JWT令牌
# 3. 在 api-tests/test-files/ 目录下准备测试图片文件
# 4. 支持的图片格式：jpg, jpeg, png, gif, webp
# 5. 单个文件最大支持5MB
# 6. folder 参数可选，用于指定上传到OSS的文件夹路径

### 错误响应示例
# 400 - 未选择文件
# 400 - 不支持的文件类型
# 400 - 文件大小超过限制
# 500 - 上传失败（OSS配置错误或网络问题）
